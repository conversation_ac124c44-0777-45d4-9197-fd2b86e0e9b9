# 在项目根目录创建 logging_config.py
import logging
import logging.handlers
import os
from datetime import datetime


def setup_logging():
    """统一的日志配置"""
    # 创建日志目录
    if not os.path.exists("log"):
        os.makedirs("log")

    # 根日志器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        "log/radar.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding="utf-8",
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()

    # 格式化器
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
