import os
import time
from collections import deque
from rich.console import Console
from rich.layout import Layout
from rich.live import Live
from rich.panel import Panel
from rich.text import Text

# --- 配置 ---
LOG_FILE_PATH = os.path.join("log", "radar.log")
MAX_LOG_LINES = 100  # 每个面板保留的最大日志行数
POLL_INTERVAL = 0.1  # 文件轮询间隔（秒）

# --- 面板样式和分类规则 ---
PANELS_CONFIG = {
    "api": {
        "title": "[bold blue]API Logs[/bold blue]",
        "color": "blue",
        "keywords": [
            "/listen_radar_state",
            "/user/login",
            "/get_scene_list",
            "/check_all_radar",
            "HTTP/1.1",
        ],
    },
    "server": {
        "title": "[bold green]雷达服务器 Logs[/bold green]",
        "color": "green",
        "keywords": ["[雷达服务器]"],
    },
    "simulator": {
        "title": "[bold magenta]模拟器 Logs[/bold magenta]",
        "color": "magenta",
        "keywords": ["[模拟器]"],
    },
    "client": {
        "title": "[bold yellow]雷达客户端/雷达 Logs[/bold yellow]",
        "color": "yellow",
        "keywords": ["[雷达("],
    },
    "worker": {
        "title": "[bold bright_cyan]Worker Logs[/bold bright_cyan]",
        "color": "bright_cyan",
        "keywords": ["[Worker]"],
    },
    "other": {
        "title": "[bold grey70]其他/未知来源 Logs[/bold grey70]",
        "color": "grey70",
        "keywords": [],
    },
}

# 预编译关键词到分类的映射
KEYWORD_MAP = []
for name, config in PANELS_CONFIG.items():
    for keyword in config["keywords"]:
        KEYWORD_MAP.append((keyword, name))

# --- UI 布局定义 ---
console = Console()
layout = Layout()

layout.split(
    Layout(name="header", size=3),
    Layout(ratio=1, name="main"),
)
layout["main"].split_row(Layout(name="left"), Layout(name="right"))
layout["left"].split_column(
    Layout(name="api"),
    Layout(name="simulator"),
    Layout(name="worker"),
)
layout["right"].split_column(
    Layout(name="server"),
    Layout(name="client"),
    Layout(name="other"),
)


def categorize_log_line(line: str) -> str:
    """根据关键词将日志行分类"""
    if "[雷达00" in line:
        return "client"
    for keyword, name in KEYWORD_MAP:
        if keyword in line:
            return name
    return "other"


def create_log_panel(category_name: str, logs: deque) -> Panel:
    """为给定的日志列表创建一个Rich Panel"""
    config = PANELS_CONFIG[category_name]

    # deque 中存储的顺序已经是 [新, ..., 旧]，直接拼接即可
    log_text = Text("\n".join(logs), style="white")
    if not logs:
        log_text = Text("暂无日志 ...", style="dim")

    # 使用默认的顶部对齐，这样新日志就会出现在最上面
    return Panel(
        log_text,
        title=config["title"],
        border_style=config["color"],
        title_align="left",
    )


def main():
    """主函数，运行实时逆序日志监控界面"""
    # 检查文件是否存在
    if not os.path.exists(LOG_FILE_PATH):
        os.makedirs(os.path.dirname(LOG_FILE_PATH) or ".", exist_ok=True)
        with open(LOG_FILE_PATH, "w", encoding="utf-8") as f:
            f.write("Log file created by monitor script.\n")

    # 初始化每个分类的日志存储
    logs = {name: deque(maxlen=MAX_LOG_LINES) for name in PANELS_CONFIG}

    # 设置标题
    header_text = Text(
        f"实时雷达系统日志监控 - 文件: {LOG_FILE_PATH}", justify="center"
    )
    layout["header"].update(Panel(header_text, style="bold blue"))

    # --- 核心逻辑 ---
    # 1. 初始填充：一次性反向读取文件来填充历史记录
    with open(LOG_FILE_PATH, "r", encoding="utf-8", errors="ignore") as file:
        all_lines = file.readlines()

        for line in reversed(all_lines):
            line = line.strip()
            if not line:
                continue
            category = categorize_log_line(line)
            if len(logs[category]) < MAX_LOG_LINES:
                logs[category].appendleft(line)  # 从左边（顶部）添加

    # 2. 实时轮询：从文件末尾开始监控新日志
    with Live(
        layout,
        console=console,
        screen=True,
        redirect_stderr=False,
        refresh_per_second=10,
    ) as live:
        try:
            # 初始化所有面板
            for name, log_deque in logs.items():
                panel = create_log_panel(name, log_deque)
                layout[name].update(panel)

            with open(LOG_FILE_PATH, "r", encoding="utf-8", errors="ignore") as file:
                file.seek(0, 2)  # 移动到文件末尾
                while True:
                    line = file.readline()
                    if not line:
                        time.sleep(POLL_INTERVAL)
                        continue

                    line = line.strip()
                    if not line:
                        continue

                    category = categorize_log_line(line)
                    logs[category].appendleft(line)  # 将新日志添加到顶部

                    # 只更新发生变化的面板
                    panel = create_log_panel(category, logs[category])
                    layout[category].update(panel)

        except KeyboardInterrupt:
            live.stop()
            console.print("[bold red]监控已停止。[/bold red]")


if __name__ == "__main__":
    main()
